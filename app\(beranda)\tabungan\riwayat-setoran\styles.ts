import { StyleSheet } from 'react-native';
import { BorderRadius, Colors, Spacing } from '../../../../constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  loadingText: {
    fontSize: 16,
    color: Colors.text_secondary,
  },

  scrollContent: {
    paddingBottom: 100, // Space for FAB
  },

  summaryContainer: {
    padding: Spacing.md,
    gap: Spacing.md,
  },

  summaryCard: {
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },

  summaryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.padding.card,
    gap: Spacing.md,
  },

  summaryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  summaryInfo: {
    flex: 1,
  },

  summaryLabel: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    marginBottom: 4,
  },

  summaryAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },

  summarySubtext: {
    fontSize: 12,
    color: Colors.text_secondary,
    marginTop: 2,
  },

  transactionContainer: {
    flex: 1,
    paddingHorizontal: Spacing.md,
  },

  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text_primary,
  },

  sectionSubtitle: {
    fontSize: 14,
    color: Colors.text_secondary,
  },

  transactionList: {
    gap: Spacing.sm,
  },

  emptyContainer: {
    alignItems: 'center',
    paddingVertical: Spacing.xl * 2,
    paddingHorizontal: Spacing.lg,
  },

  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text_primary,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },

  emptySubtitle: {
    fontSize: 14,
    color: Colors.text_secondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: Spacing.lg,
  },

  emptyButton: {
    minWidth: 160,
  },

  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },

  fabGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
