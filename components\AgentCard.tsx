import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { BorderRadius, Colors, Shadows, Spacing } from '../constants/theme';


interface AgentCardProps {
  agentName: string;
  onPress?: () => void;
  style?: any;
}

export const AgentCard: React.FC<AgentCardProps> = ({
  agentName,
  onPress,
  style
}) => {
  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.9}
      accessibilityRole="button"
      accessibilityLabel={`Agent ${agentName}, siap membantu Anda`}
      accessibilityHint="Ketuk untuk menghubungi agent melalui chat"
      accessible={true}
    >
      <LinearGradient
        colors={[Colors.neutral_light, Colors.background]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* Avatar Section */}
          <View style={styles.avatarSection}>
            <View style={styles.avatarContainer}>
              <LinearGradient
                colors={Colors.gradients.primary as any}
                style={styles.avatarGradient}
              >
                <Ionicons name="person" size={28} color="#fff" />
              </LinearGradient>
              
              {/* Status badge */}
              <View style={styles.statusBadge}>
                <Ionicons name="checkmark" size={10} color="#fff" />
              </View>
            </View>
          </View>

          {/* Info Section */}
          <View style={styles.infoSection}>
            <Text style={styles.agentName}>{agentName}</Text>
            
            <View style={styles.statusContainer}>
              <View style={styles.statusDot} />
              <Text style={styles.statusText}>Siap Membantu</Text>
            </View>
            
            <Text style={styles.experienceText}>Agent Terpercaya</Text>
          </View>

          {/* Action Section */}
          <View style={styles.actionSection}>
            <View style={styles.actionButton}>
              <LinearGradient
                colors={Colors.gradients.primary as any}
                style={styles.actionGradient}
              >
                <Ionicons name="chatbubble" size={20} color="#fff" />
              </LinearGradient>
            </View>
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
    ...Shadows.card,
  },
  
  gradient: {
    padding: Spacing.padding.card,
  },
  
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  
  avatarSection: {
    position: 'relative',
  },
  
  avatarContainer: {
    position: 'relative',
  },
  
  avatarGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  statusBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: Colors.success,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },
  
  infoSection: {
    flex: 1,
    gap: 4,
  },
  
  agentName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text_primary,
  },
  
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.success,
  },
  
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.success,
  },

  experienceText: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.text_secondary,
  },
  
  actionSection: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  actionButton: {
    borderRadius: BorderRadius.md,
    overflow: 'hidden',
  },
  
  actionGradient: {
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default AgentCard;
