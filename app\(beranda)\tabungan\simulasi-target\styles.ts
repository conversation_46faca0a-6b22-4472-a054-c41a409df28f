import { StyleSheet } from 'react-native';
import { BorderRadius, Colors, Spacing } from '../../../../constants/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  
  scrollContent: {
    paddingBottom: 100, // Space for navigation bar
  },
  
  headerGradient: {
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.md,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    marginBottom: Spacing.lg,
  },
  
  header: {
    alignItems: 'center',
  },
  
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    textAlign: 'center',
  },
  
  headerSubtitle: {
    fontSize: 16,
    color: Colors.light,
    opacity: 0.9,
    textAlign: 'center',
  },
  
  currentSavingsSection: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
  },
  
  currentSavingsCard: {
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  
  currentSavingsGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
    gap: Spacing.md,
  },
  
  currentSavingsInfo: {
    flex: 1,
  },
  
  currentSavingsLabel: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    marginBottom: 4,
  },
  
  currentSavingsAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  
  tipsContainer: {
    gap: Spacing.md,
  },
  
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.sm,
    padding: Spacing.sm,
    backgroundColor: Colors.neutral_light,
    borderRadius: BorderRadius.sm,
  },
  
  tipText: {
    flex: 1,
    fontSize: 14,
    color: Colors.text_primary,
    lineHeight: 20,
  },
});
