import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { ActivityIndicator, StyleSheet, Text, TouchableOpacity, ViewStyle } from 'react-native';
import { BorderRadius, Colors, Shadows, Spacing } from '../../constants/theme';

interface ActionButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  icon?: keyof typeof Ionicons.glyphMap;
  loading?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
}

export const ActionButton: React.FC<ActionButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  icon,
  loading = false,
  disabled = false,
  style
}) => {
  const getButtonColors = (): [string, string] => {
    switch (variant) {
      case 'primary':
        return [Colors.base, Colors.medium];
      case 'secondary':
        return [Colors.light, Colors.neutral_light];
      case 'outline':
        return ['transparent', 'transparent'];
      default:
        return [Colors.base, Colors.medium];
    }
  };

  const getTextColor = () => {
    switch (variant) {
      case 'primary':
        return '#fff';
      case 'secondary':
        return Colors.text_primary;
      case 'outline':
        return Colors.base;
      default:
        return '#fff';
    }
  };

  const getIconColor = () => {
    return getTextColor();
  };

  const isDisabled = disabled || loading;

  return (
    <TouchableOpacity
      style={[
        styles.container,
        variant === 'outline' && styles.outlineContainer,
        isDisabled && styles.disabledContainer,
        style
      ]}
      onPress={onPress}
      disabled={isDisabled}
      activeOpacity={0.8}
      accessibilityRole="button"
      accessibilityLabel={title}
      accessibilityState={{ disabled: isDisabled }}
    >
      <LinearGradient
        colors={getButtonColors()}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={[
          styles.gradient,
          isDisabled && styles.disabledGradient
        ]}
      >
        {loading ? (
          <ActivityIndicator size="small" color={getTextColor()} />
        ) : (
          <>
            {icon && (
              <Ionicons 
                name={icon} 
                size={20} 
                color={getIconColor()} 
                style={styles.icon}
              />
            )}
            <Text style={[styles.text, { color: getTextColor() }]}>
              {title}
            </Text>
          </>
        )}
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: BorderRadius.button,
    overflow: 'hidden',
    ...Shadows.button,
  },
  
  outlineContainer: {
    borderWidth: 2,
    borderColor: Colors.base,
  },
  
  disabledContainer: {
    opacity: 0.6,
  },
  
  gradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    minHeight: 56, // Accessibility touch target
    gap: Spacing.sm,
  },
  
  disabledGradient: {
    opacity: 0.5,
  },
  
  icon: {
    marginRight: Spacing.xs,
  },
  
  text: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default ActionButton;
