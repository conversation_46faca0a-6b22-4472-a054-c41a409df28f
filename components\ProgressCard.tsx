import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { AnimatedCircularProgress } from 'react-native-circular-progress';
import { BorderRadius, Colors, Shadows, Spacing } from '../constants/theme';
import { formatCurrency } from '../utils/designSystem';

interface ProgressCardProps {
  currentAmount: number;
  targetAmount: number;
  onPress?: () => void;
  style?: any;
}

export const ProgressCard: React.FC<ProgressCardProps> = ({
  currentAmount,
  targetAmount,
  onPress,
  style
}) => {
  const progressPercentage = Math.round((currentAmount / targetAmount) * 100);
  const remainingAmount = targetAmount - currentAmount;

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.95}
      accessibilityRole="button"
      accessibilityLabel={`Progress tabungan ${progressPercentage} persen. Terkumpul ${formatCurrency(currentAmount)} dari target ${formatCurrency(targetAmount)}`}
      accessibilityHint="Ketuk untuk melihat detail progress dan riwayat tabungan"
      accessible={true}
    >
      <LinearGradient
        colors={Colors.gradients.light as any}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* Left side - Radial Chart */}
          <View style={styles.chartContainer}>
            <AnimatedCircularProgress
              size={100}
              width={8}
              fill={progressPercentage}
              tintColor={Colors.base}
              backgroundColor={Colors.light}
              rotation={0}
              lineCap="round"
              duration={1000}
            >
              {() => (
                <View style={styles.chartCenter}>
                  <Text style={styles.percentageText}>{progressPercentage}%</Text>
                </View>
              )}
            </AnimatedCircularProgress>
          </View>

          {/* Right side - Amount Info */}
          <View style={styles.infoContainer}>
            <View style={styles.amountSection}>
              <Text style={styles.label}>Terkumpul</Text>
              <Text style={styles.currentAmount}>
                {formatCurrency(currentAmount)}
              </Text>
            </View>

            <View style={styles.targetSection}>
              <Text style={styles.label}>Target</Text>
              <Text style={styles.targetAmount}>
                {formatCurrency(targetAmount)}
              </Text>
            </View>

            <View style={styles.remainingSection}>
              <Text style={styles.remainingLabel}>Sisa</Text>
              <Text style={styles.remainingAmount}>
                {formatCurrency(remainingAmount)}
              </Text>
            </View>
          </View>
        </View>

        {/* Progress indicator at bottom */}
        <View style={styles.progressIndicator}>
          <View style={styles.progressBar}>
            <LinearGradient
              colors={Colors.gradients.primary as any}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={[
                styles.progressFill,
                { width: `${Math.min(progressPercentage, 100)}%` }
              ]}
            />
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
    ...Shadows.card,
    // Subtle organic rotation for natural feel
    transform: [{ rotate: '0.5deg' }],
  },
  
  gradient: {
    padding: Spacing.padding.card,
  },
  
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.lg,
  },
  
  chartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  chartCenter: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  percentageText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.base,
  },
  
  infoContainer: {
    flex: 1,
    gap: Spacing.sm,
  },
  
  amountSection: {
    marginBottom: Spacing.xs,
  },
  
  targetSection: {
    marginBottom: Spacing.xs,
  },
  
  remainingSection: {
    paddingTop: Spacing.xs,
    borderTopWidth: 1,
    borderTopColor: Colors.light,
  },
  
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text_secondary,
    marginBottom: 2,
  },

  currentAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.base,
  },

  targetAmount: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text_primary,
  },

  remainingLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text_secondary,
    marginBottom: 2,
  },

  remainingAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.medium,
  },
  
  progressIndicator: {
    marginTop: Spacing.md,
  },
  
  progressBar: {
    height: 6,
    backgroundColor: Colors.light,
    borderRadius: BorderRadius.pill,
    overflow: 'hidden',
  },
  
  progressFill: {
    height: '100%',
    borderRadius: BorderRadius.pill,
  },
});

export default ProgressCard;
