import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  RefreshControl,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { ActionButton } from '../../../../components/tabungan/ActionButton';
import { TransactionCard } from '../../../../components/tabungan/TransactionCard';
import { BorderRadius, Colors, Spacing } from '../../../../constants/theme';
import { formatCurrency } from '../../../../utils/designSystem';

interface Setoran {
  id: string;
  amount: number;
  date: string;
  note?: string;
  createdAt: string;
}

export default function RiwayatSetoranScreen() {
  const [setoranList, setSetoranList] = useState<Setoran[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Mock data - in real app, this would come from database
  const mockData: Setoran[] = [
    {
      id: '1',
      amount: 500000,
      date: '2024-01-15',
      note: 'Setoran rutin bulanan',
      createdAt: '2024-01-15T10:30:00Z',
    },
    {
      id: '2',
      amount: 250000,
      date: '2024-01-08',
      note: 'Bonus dari pekerjaan',
      createdAt: '2024-01-08T14:20:00Z',
    },
    {
      id: '3',
      amount: 100000,
      date: '2024-01-01',
      note: 'Setoran awal tahun',
      createdAt: '2024-01-01T09:00:00Z',
    },
    {
      id: '4',
      amount: 750000,
      date: '2023-12-25',
      note: 'Bonus akhir tahun',
      createdAt: '2023-12-25T16:45:00Z',
    },
    {
      id: '5',
      amount: 300000,
      date: '2023-12-15',
      note: 'Setoran rutin',
      createdAt: '2023-12-15T11:20:00Z',
    },
  ];

  useEffect(() => {
    loadSetoranData();
  }, []);

  const loadSetoranData = async () => {
    try {
      setLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Sort by date (newest first)
      const sortedData = mockData.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
      
      setSetoranList(sortedData);
    } catch (error) {
      console.error('Error loading setoran data:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadSetoranData();
    setRefreshing(false);
  };

  const getTotalAmount = () => {
    return setoranList.reduce((total, setoran) => total + setoran.amount, 0);
  };

  const handleAddSetoran = () => {
    router.push('/(beranda)/tabungan/tambah-setoran');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const getMonthlyStats = () => {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    const thisMonthSetoran = setoranList.filter(setoran => {
      const setoranDate = new Date(setoran.date);
      return setoranDate.getMonth() === currentMonth && 
             setoranDate.getFullYear() === currentYear;
    });

    const thisMonthTotal = thisMonthSetoran.reduce((total, setoran) => total + setoran.amount, 0);
    const thisMonthCount = thisMonthSetoran.length;

    return { total: thisMonthTotal, count: thisMonthCount };
  };

  const monthlyStats = getMonthlyStats();

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Memuat data setoran...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Colors.base]}
            tintColor={Colors.base}
          />
        }
      >
        {/* Summary Cards */}
        <View style={styles.summaryContainer}>
          {/* Total Savings Card */}
          <LinearGradient
            colors={[Colors.base, Colors.medium]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.summaryCard}
          >
            <View style={styles.summaryContent}>
              <View style={styles.summaryIcon}>
                <Ionicons name="wallet" size={24} color="#fff" />
              </View>
              <View style={styles.summaryInfo}>
                <Text style={styles.summaryLabel}>Total Tabungan</Text>
                <Text style={styles.summaryAmount}>
                  {formatCurrency(getTotalAmount())}
                </Text>
              </View>
            </View>
          </LinearGradient>

          {/* Monthly Stats Card */}
          <LinearGradient
            colors={[Colors.light, Colors.neutral_light]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.summaryCard}
          >
            <View style={styles.summaryContent}>
              <View style={[styles.summaryIcon, { backgroundColor: Colors.base }]}>
                <Ionicons name="calendar" size={24} color="#fff" />
              </View>
              <View style={styles.summaryInfo}>
                <Text style={[styles.summaryLabel, { color: Colors.text_primary }]}>
                  Bulan Ini
                </Text>
                <Text style={[styles.summaryAmount, { color: Colors.text_primary }]}>
                  {formatCurrency(monthlyStats.total)}
                </Text>
                <Text style={styles.summarySubtext}>
                  {monthlyStats.count} setoran
                </Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Transaction List */}
        <View style={styles.transactionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Riwayat Setoran</Text>
            <Text style={styles.sectionSubtitle}>
              {setoranList.length} transaksi
            </Text>
          </View>

          {setoranList.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="document-text-outline" size={64} color={Colors.text_secondary} />
              <Text style={styles.emptyTitle}>Belum Ada Setoran</Text>
              <Text style={styles.emptySubtitle}>
                Mulai menabung dengan menambah setoran pertama Anda
              </Text>
              <ActionButton
                title="Tambah Setoran"
                onPress={handleAddSetoran}
                icon="add"
                style={styles.emptyButton}
              />
            </View>
          ) : (
            <View style={styles.transactionList}>
              {setoranList.map((setoran) => (
                <TransactionCard
                  key={setoran.id}
                  id={setoran.id}
                  amount={setoran.amount}
                  date={formatDate(setoran.date)}
                  note={setoran.note}
                  createdAt={setoran.date}
                />
              ))}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Floating Action Button */}
      {setoranList.length > 0 && (
        <TouchableOpacity
          style={styles.fab}
          onPress={handleAddSetoran}
          activeOpacity={0.8}
          accessibilityRole="button"
          accessibilityLabel="Tambah setoran baru"
        >
          <LinearGradient
            colors={[Colors.base, Colors.medium]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.fabGradient}
          >
            <Ionicons name="add" size={28} color="#fff" />
          </LinearGradient>
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  loadingText: {
    fontSize: 16,
    color: Colors.text_secondary,
  },

  scrollContent: {
    paddingBottom: 100, // Space for FAB
  },

  summaryContainer: {
    padding: Spacing.md,
    gap: Spacing.md,
  },

  summaryCard: {
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },

  summaryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.padding.card,
    gap: Spacing.md,
  },

  summaryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  summaryInfo: {
    flex: 1,
  },

  summaryLabel: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    marginBottom: 4,
  },

  summaryAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },

  summarySubtext: {
    fontSize: 12,
    color: Colors.text_secondary,
    marginTop: 2,
  },

  transactionContainer: {
    flex: 1,
    paddingHorizontal: Spacing.md,
  },

  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text_primary,
  },

  sectionSubtitle: {
    fontSize: 14,
    color: Colors.text_secondary,
  },

  transactionList: {
    gap: Spacing.sm,
  },

  emptyContainer: {
    alignItems: 'center',
    paddingVertical: Spacing.xl * 2,
    paddingHorizontal: Spacing.lg,
  },

  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text_primary,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },

  emptySubtitle: {
    fontSize: 14,
    color: Colors.text_secondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: Spacing.lg,
  },

  emptyButton: {
    minWidth: 160,
  },

  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },

  fabGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
