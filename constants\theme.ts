// TabunganBerkah - Tabungan Haji Design System
// Based on JSON specification for professional, elderly-friendly UI

export const Colors = {
  // Base palette - monochromatic blue
  base: '#0055AA',
  light: '#AED6F1',
  medium: '#4479CA',
  dark: '#003366',
  background: '#FFFFFF',
  neutral_light: '#F5F8FC',
  text_primary: '#003366',
  text_secondary: '#4479CA',
  
  // Additional colors for status and accents
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  
  // Gradient combinations
  gradients: {
    primary: ['#0055AA', '#4479CA'],
    light: ['#AED6F1', '#F5F8FC'],
    card: ['#E3F2FD', '#BBDEFB'],
  }
};

export const Typography = {
  // Font families
  fonts: {
    primary: 'Inter_400Regular', // Sans-serif modern
    primaryMedium: 'Inter_500Medium',
    primarySemiBold: 'Inter_600SemiBold',
    primaryBold: 'Inter_700Bold',
    secondary: 'PlayfairDisplay_400Regular', // Serif ringan
    secondaryBold: 'PlayfairDisplay_700Bold',
    fallback: 'System'
  },
  
  // Font sizes - elderly-friendly scale
  sizes: {
    h1: 24,
    h2: 20,
    body: 16,
    caption: 14,
    small: 12
  },
  
  // Font weights
  weights: {
    light: '300',
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700'
  },
  
  // Line heights for readability
  lineHeights: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6
  }
};

export const Spacing = {
  // 8px grid system
  grid: 8,
  
  // Standard spacing values
  xs: 4,   // 0.5 * grid
  sm: 8,   // 1 * grid
  md: 16,  // 2 * grid (margins)
  lg: 24,  // 3 * grid
  xl: 32,  // 4 * grid
  xxl: 48, // 6 * grid
  
  // Component-specific spacing
  margins: 16,
  padding: {
    card: 20,
    button: 16,
    input: 12
  }
};

export const BorderRadius = {
  // Rounded values for organic shapes
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,  // Standard card radius
  xl: 24,
  pill: 999, // For pill-rounded elements
  
  // Component-specific
  card: 16,
  button: 12,
  input: 8
};

export const Shadows = {
  // Soft shadows for depth
  card: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  
  button: {
    shadowColor: '#0055AA',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  
  floating: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 8,
  }
};

export const Layout = {
  // Touch targets for accessibility
  touchTarget: {
    minHeight: 48,
    minWidth: 48
  },
  
  // Container dimensions
  container: {
    maxWidth: 400,
    padding: 16
  },
  
  // Component dimensions
  card: {
    minHeight: 80,
    padding: 20
  },
  
  button: {
    height: 48,
    paddingHorizontal: 24
  }
};

export const Animation = {
  // Smooth, natural animations
  duration: {
    fast: 200,
    normal: 300,
    slow: 500
  },
  
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out'
  }
};

// Component-specific theme configurations
export const ComponentThemes = {
  progressCard: {
    borderRadius: BorderRadius.card,
    backgroundColor: Colors.light,
    padding: Spacing.padding.card,
    shadow: Shadows.card
  },
  
  timeRemainingCard: {
    borderRadius: BorderRadius.pill,
    backgroundColor: Colors.neutral_light,
    padding: Spacing.md,
    minHeight: Layout.touchTarget.minHeight
  },
  
  agentCard: {
    borderRadius: BorderRadius.card,
    backgroundColor: Colors.neutral_light,
    padding: Spacing.padding.card,
    shadow: Shadows.card
  },
  
  navigationBar: {
    borderRadius: BorderRadius.xl,
    backgroundColor: Colors.background,
    height: 80,
    paddingBottom: 10,
    paddingTop: 10,
    shadow: Shadows.floating
  }
};

// Accessibility configurations
export const Accessibility = {
  contrastRatio: 'WCAG AA+',
  touchSize: Layout.touchTarget,
  screenReader: true,
  
  // High contrast colors for better visibility
  highContrast: {
    text: Colors.text_primary,
    background: Colors.background,
    accent: Colors.base
  }
};

export default {
  Colors,
  Typography,
  Spacing,
  BorderRadius,
  Shadows,
  Layout,
  ComponentThemes,
  Accessibility
};
