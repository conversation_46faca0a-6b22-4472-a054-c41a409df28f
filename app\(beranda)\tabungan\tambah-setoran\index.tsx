import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  View,
  Text,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { FormCard } from '../../../../components/tabungan/FormCard';
import { InputField } from '../../../../components/tabungan/InputField';
import { ActionButton } from '../../../../components/tabungan/ActionButton';
import { Colors, Spacing, BorderRadius } from '../../../../constants/theme';
import { formatCurrency } from '../../../../utils/designSystem';

export default function TambahSetoranScreen() {
  const [amount, setAmount] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [note, setNote] = useState('');
  const [loading, setLoading] = useState(false);
  const [focusedInput, setFocusedInput] = useState<string | null>(null);

  const formatCurrencyInput = (value: string) => {
    // Remove non-numeric characters
    const numericValue = value.replace(/[^0-9]/g, '');
    
    // Format with thousand separators
    if (numericValue) {
      return new Intl.NumberFormat('id-ID').format(parseInt(numericValue));
    }
    return '';
  };

  const handleAmountChange = (text: string) => {
    const formatted = formatCurrencyInput(text);
    setAmount(formatted);
  };

  const getNumericAmount = () => {
    return parseInt(amount.replace(/[^0-9]/g, '')) || 0;
  };

  const formatDisplayCurrency = (numericAmount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(numericAmount);
  };

  const handleSubmit = async () => {
    const numericAmount = getNumericAmount();
    
    if (!numericAmount || numericAmount <= 0) {
      Alert.alert('Kesalahan', 'Harap masukkan jumlah setoran yang valid');
      return;
    }

    if (!date) {
      Alert.alert('Kesalahan', 'Harap pilih tanggal setoran');
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'Berhasil!',
        `Setoran sebesar ${formatDisplayCurrency(numericAmount)} berhasil disimpan.`,
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Kesalahan', 'Terjadi kesalahan saat menyimpan setoran');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <LinearGradient
            colors={[Colors.base, Colors.medium]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.headerGradient}
          >
            <View style={styles.header}>
              <Text style={styles.headerTitle}>💰 Tambah Setoran Baru</Text>
              <Text style={styles.headerSubtitle}>
                Catat setoran tabungan haji Anda
              </Text>
            </View>
          </LinearGradient>

          {/* Form */}
          <FormCard
            title="Detail Setoran"
            subtitle="Masukkan informasi setoran dengan lengkap"
          >
            <InputField
              label="Jumlah Setoran *"
              icon="wallet"
              value={amount}
              onChangeText={handleAmountChange}
              placeholder="Masukkan jumlah setoran"
              keyboardType="numeric"
              onFocus={() => setFocusedInput('amount')}
              onBlur={() => setFocusedInput(null)}
              focused={focusedInput === 'amount'}
            />

            {amount && (
              <View style={styles.currencyDisplay}>
                <Text style={styles.currencyText}>
                  {formatDisplayCurrency(getNumericAmount())}
                </Text>
              </View>
            )}

            <InputField
              label="Tanggal Setoran *"
              icon="calendar"
              value={date}
              onChangeText={setDate}
              placeholder="YYYY-MM-DD"
              onFocus={() => setFocusedInput('date')}
              onBlur={() => setFocusedInput(null)}
              focused={focusedInput === 'date'}
            />

            <InputField
              label="Catatan (Opsional)"
              icon="document-text"
              value={note}
              onChangeText={setNote}
              placeholder="Tambahkan catatan untuk setoran ini..."
              multiline
              numberOfLines={3}
              style={{ textAlignVertical: 'top' }}
              onFocus={() => setFocusedInput('note')}
              onBlur={() => setFocusedInput(null)}
              focused={focusedInput === 'note'}
            />
          </FormCard>

          {/* Action Buttons */}
          <View style={styles.actionContainer}>
            <ActionButton
              title={loading ? 'Menyimpan...' : 'Simpan Setoran'}
              onPress={handleSubmit}
              icon="checkmark-circle"
              loading={loading}
              disabled={loading}
            />
          </View>

          {/* Info Card */}
          <FormCard
            title="Informasi"
            subtitle="Tips untuk menambah setoran"
          >
            <View style={styles.infoContent}>
              <Ionicons name="information-circle" size={24} color={Colors.base} />
              <Text style={styles.infoText}>
                Pastikan jumlah dan tanggal setoran sudah benar sebelum menyimpan. 
                Data yang tersimpan akan mempengaruhi perhitungan progress tabungan Anda.
              </Text>
            </View>
          </FormCard>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  
  keyboardView: {
    flex: 1,
  },
  
  scrollContent: {
    paddingBottom: 100, // Space for navigation bar
  },
  
  headerGradient: {
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.md,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    marginBottom: Spacing.lg,
  },
  
  header: {
    alignItems: 'center',
  },
  
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    textAlign: 'center',
  },
  
  headerSubtitle: {
    fontSize: 16,
    color: Colors.light,
    opacity: 0.9,
    textAlign: 'center',
  },
  
  currencyDisplay: {
    backgroundColor: Colors.neutral_light,
    padding: Spacing.sm,
    borderRadius: BorderRadius.sm,
    marginTop: -Spacing.sm,
    marginBottom: Spacing.sm,
  },
  
  currencyText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.base,
    textAlign: 'center',
  },
  
  actionContainer: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
  },
  
  infoContent: {
    flexDirection: 'row',
    gap: Spacing.md,
    alignItems: 'flex-start',
  },
  
  infoText: {
    flex: 1,
    fontSize: 14,
    color: Colors.text_primary,
    lineHeight: 20,
  },
});
