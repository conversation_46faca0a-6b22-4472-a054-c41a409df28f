# TabunganBerkah - Aplikasi Tabungan Haji 🕌

TabunganBerkah adalah aplikasi mobile untuk mengelola tabungan haji yang dirancang khusus untuk kemudahan pengguna lansia dengan antarmuka yang ramah dan profesional.

## Fitur Utama
- 📊 Tracking progress tabungan haji
- 💰 Pencatatan setoran rutin
- 📈 Simulasi target tabungan
- 👥 Manajemen agen tabungan
- 🔐 Autentikasi yang aman

This is an [Expo](https://expo.dev) project created with [`create-expo-app`](https://www.npmjs.com/package/create-expo-app).

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
   npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Get a fresh project

When you're ready, run:

```bash
npm run reset-project
```

This command will move the starter code to the **app-example** directory and create a blank **app** directory where you can start developing.

## Icon dan Branding

Aplikasi ini menggunakan tema Islami dengan:
- **Nama**: TabunganBerkah (sebelumnya SaveMoney)
- **Icon**: Emoji masjid 🕌 atau icon masjid/Ka'bah yang sesuai
- **Warna**: Skema biru monokromatik (#0055AA) yang profesional
- **Target**: Pengguna lansia dengan UI yang ramah dan mudah digunakan

### Mengganti Icon
Untuk mengganti icon aplikasi:
1. Siapkan file PNG dengan ukuran 1024x1024 px
2. Gunakan desain masjid atau Ka'bah dengan warna biru (#0055AA)
3. Ganti file di `assets/images/icon.png`
4. Ganti juga `adaptive-icon.png` untuk Android
5. Jalankan `expo prebuild --clear` untuk memperbarui

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.
