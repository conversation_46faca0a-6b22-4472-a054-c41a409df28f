import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React from 'react';
import { SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { AgentCard } from '../../components/AgentCard';
import { ProgressCard } from '../../components/ProgressCard';
import { TimeRemainingCard } from '../../components/TimeRemainingCard';
import { BorderRadius, Colors, Shadows, Spacing } from '../../constants/theme';
import { useAuth } from '../../contexts/AuthContext';



export default function HomeScreen() {
  const { user, signOut } = useAuth();

  // Mock data - in real app, this would come from database
  const savingsData = {
    currentAmount: 2500000,
    targetAmount: 10000000,
    selectedAgent: 'Ibu Sari',
    monthsRemaining: 8
  };

  const handleSignOut = async () => {
    await signOut();
    router.replace('/(auth)/login');
  };

  const handleProgressCardPress = () => {
    // Navigate to detailed progress view
    console.log('Progress card pressed');
  };

  const handleAgentPress = () => {
    // Navigate to agent contact
    console.log('Agent card pressed');
  };



  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header with Gradient */}
        <LinearGradient
          colors={[Colors.base, Colors.medium]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <Text style={styles.appTitle}>🕌 Tabungan Berkah</Text>
              <Text style={styles.headerSubtitle}>Tabungan Haji Terpercaya</Text>
            </View>
            <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
              <Ionicons name="log-out-outline" size={22} color="#fff" />
            </TouchableOpacity>
          </View>
        </LinearGradient>

        {/* Welcome Card */}
        <View style={styles.welcomeCard}>
          <View style={styles.welcomeHeader}>
            <View style={styles.avatarContainer}>
              <Ionicons name="person-circle" size={50} color={Colors.base} />
            </View>
            <View style={styles.welcomeInfo}>
              <Text style={styles.welcomeText}>Selamat Datang!</Text>
              <Text style={styles.userEmail}>{user?.email}</Text>
            </View>
          </View>
        </View>

        {/* Main Progress Card */}
        <ProgressCard
          currentAmount={savingsData.currentAmount}
          targetAmount={savingsData.targetAmount}
          onPress={handleProgressCardPress}
        />

        {/* Time Remaining Card */}
        <View style={styles.timeRemainingContainer}>
          <TimeRemainingCard monthsRemaining={savingsData.monthsRemaining} />
        </View>

        {/* Agent Card */}
        <View style={styles.agentSection}>
          <Text style={styles.sectionTitle}>Agent Anda</Text>
          <AgentCard
            agentName={savingsData.selectedAgent}
            onPress={handleAgentPress}
          />
        </View>


      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  scrollContent: {
    paddingBottom: 100, // Space for navigation bar
  },

  headerGradient: {
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.md,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    marginBottom: Spacing.lg,
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  headerLeft: {
    flex: 1,
  },

  appTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },

  headerSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light,
    opacity: 0.9,
  },

  signOutButton: {
    width: 44,
    height: 44,
    borderRadius: BorderRadius.md,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  welcomeCard: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.card,
    padding: Spacing.padding.card,
    ...Shadows.card,
  },

  welcomeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },

  avatarContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.neutral_light,
    alignItems: 'center',
    justifyContent: 'center',
  },

  welcomeInfo: {
    flex: 1,
  },

  welcomeText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text_primary,
    marginBottom: 4,
  },

  userEmail: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.text_secondary,
  },

  timeRemainingContainer: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
    alignItems: 'flex-start',
  },

  agentSection: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
  },

  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text_primary,
    marginBottom: Spacing.md,
  },
});